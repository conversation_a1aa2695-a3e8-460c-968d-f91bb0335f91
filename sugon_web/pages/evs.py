from sugon_web.pages.login import LoginPage
from sugon_web.common.elements import EVSElements


class EvsPage(LoginPage):

    def __init__(self, page, env):
        super().__init__(page, env)

    def navigate_to_evs(self):
        """导航到EVS页面"""
        self._ensure_login(name=self.env["username"], pwd=self.env["password"])
        self.navigate_to_service('云硬盘')


    def evs_create(self,
                   name,
                   count=1,
                   size=30,
                   empty=False,
                   image_name="xstor-test",
                   volume_type="xstor-type",
                   desc=""):
        """创建云硬盘"""
        self.btn_create.click()
        self.fill(EVSElements.name, name)

        if count != 1:
            self.get_by_label("数量").fill(count)
        if empty:
            self._select_image_source()
        self._select_volume_type(volume_type)
        if empty:
            self._select_image(image_name)

        self._input_size.fill(str(size))
        self.input_desc.fill(desc)
        self.btn_confirm.click()

    @property
    def input_desc(self):
        """定位描述输入框"""
        return self.locator("textarea")

    def _select_image_source(self):
        """选择云硬盘来源为镜像"""
        self.get_by_role("dialog", name="dialog").get_by_placeholder("请选择", exact=True).click()
        self.get_by_has_text("镜像", exact=True).click()

    def _select_volume_type(self, name):
        """选择云硬盘类型"""
        self.get_by_placeholder("请选择类型").click()
        self.get_by_has_text(name, exact=True).click()

    def _select_image(self, name):
        """选择镜像"""
        self.locator(EVSElements.image).click()
        self.get_by_has_text(name, exact=True, initial_selector="ul").get_by_role("listitem").click()

    def _select_mode(self, mode):
        """选择云硬盘模式"""
        self.get_by_placeholder("请选择模式").click()
        self.get_by_has_text(mode, exact=True).click()

    @property
    def _input_size(self):
        """输入云硬盘大小"""
        return self.get_by_label("slider between 1 and").get_by_role("spinbutton")

    def _btn_operation(self, name):
        """定位资源操作按钮元素"""
        return self.get_by_role("row", name=name).get_by_role("button")

    def evs_delete(self, name):
        """定位资源删除按钮元素"""
        self._btn_operation(name)
        self.locator('[id^="dropdown-menu-"]').get_by_text("删除").click()
        self.btn_confirm.click()
