from sugon_web.pages.login import LoginPage


class EvsPage(LoginPage):

    def __init__(self, page, env):
        super().__init__(page, env)

    def navigate_to_evs(self):
        """导航到EVS页面"""
        self._ensure_login(name=self.env["username"], pwd=self.env["password"])
        self.navigate_to_service('云硬盘')


    def evs_create(self, name, count=1, size=50, az="Autotest", volume_type="xstor-type", desc=""):
        """创建云硬盘"""
        self.click('新建')

        self.fill("//form/div/div/div/input", name)

        if count != 1:
            self.fill("//form/div[2]/div/div/div/input", count)

        self.get_by_placeholder("请选择类型").click()
        self.locator("li").filter(has_text=volume_type).click()

        self.get_by_placeholder("请选择模式").click()
        self.locator("li").filter(has_text="精简置备").click()

        self.locator("textarea").click()
        self.locator("textarea").fill(desc)

        self.dialog_confirm.click()

