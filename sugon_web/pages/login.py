from playwright.sync_api import Locator
from sugon_web.common.base import BasePage

class LoginPage(BasePage):

    def __init__(self, page, env):
        super().__init__(page, env)  # 调用父类构造方法

    def login(self, name: str, pwd: str):
        """执行登录操作"""
        self._input_username.fill(name)
        self._input_password.fill(pwd)
        self._btn_login.click()

    @property
    def _input_username(self) -> Locator:
        return self.get_by_placeholder("请输入登录账号")

    @property
    def _input_password(self) -> Locator:
        return self.get_by_placeholder("请输入登录密码")

    @property
    def _btn_login(self) -> Locator:
        return self.get_by_text("登 录")

    def is_logged_in(self)-> bool:
        """检查是否已登录"""
        try:
            # 登录成功后，登录表单应该消失
            self._input_username.wait_for(state="hidden", timeout=2000)
            return True
        except:
            return False

    def _ensure_login(self, name: str, pwd: str):
        """确保已登录，如果未登录则执行登录"""
        if not self.is_logged_in():
            self.login(name, pwd)
            self.close_dialog_if_exists()
            assert self.is_logged_in(), "登录失败"

