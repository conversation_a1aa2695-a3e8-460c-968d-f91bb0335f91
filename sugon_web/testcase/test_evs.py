import re

class TestEVS:

    def test_volume_create(self, evs_page):
        """云硬盘创建功能验证"""

        evs_page.locator("div").filter(has_text=re.compile(r"^云硬盘$")).nth(1).click()

        name = "volume-test"
        evs_page.evs_create(name, desc="1235")

        evs_page.assert_popup_success()
        evs_page.assert_status(name, status="可用")
        evs_page.evs_delete(name)
        evs_page.assert_deleted(name)

    def test_volume_search(self, evs_page):
        """云硬盘页面列表搜索结果验证"""

        evs_page.locator("div").filter(has_text=re.compile(r"^云硬盘$")).nth(2).click()

        keyword = "volume-test"
        evs_page.search(keyword)

        evs_page.assert_list_contain(keyword)


class TestEVSS:

    pass
