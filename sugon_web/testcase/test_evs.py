import re
import pytest
from sugon_web.utils.util import random_data

class TestEVS:

    @pytest.mark.parametrize("empty, size, desc", [(True, 30, "页面测试描述"),(False, 50, "test_description")])
    def test_volume_create(self, evs_page, empty, size, desc):
        """云硬盘创建功能验证"""

        evs_page.locator("div").filter(has_text=re.compile(r"^云硬盘$")).nth(1).click()

        name=random_data()
        evs_page.evs_create(name, empty=empty, size=size, desc=desc)
        evs_page.assert_popup_success()
        evs_page.assert_status(name, status="可用")

        evs_page.evs_delete(name)
        evs_page.assert_deleted(name)

    def test_volume_search(self, evs_page, volume_name):
        """云硬盘页面列表搜索结果验证"""

        evs_page.search(volume_name)
        evs_page.assert_list_contain(volume_name)


class TestEVSS:

    pass
