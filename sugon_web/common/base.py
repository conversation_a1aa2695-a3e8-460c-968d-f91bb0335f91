import re
from playwright.sync_api import Page, expect
from sugon_web.common.elements import BaseElements
from sugon_web.utils.logger import logger


def _format_selector(selector):
    """格式化选择器，自动处理文本选择器的引号

    Args:
        selector: 原始选择器

    Return:
        str: 格式化后的选择器
    """
    # 如果已经有引号，保持原样
    if selector.startswith(('"', "'")):
        return selector

    # 如果是明显的CSS选择器，保持原样
    if selector.startswith(('#', '.', '[', '(')) or '//' in selector:
        return selector

    # 其他情况当作文本处理，添加双引号
    return f'"{selector}"'


class Base:

    def __init__(self, page: Page, env: dict):
        self.page = page
        self.env = env  # 接收环境配置
        self.logger = logger  # 传递日志工具

    @property
    def base_url(self):
        """动态获取当前环境的base_url"""
        return self.env["url"]

    def locator(self, selector):
        """根据选择器定位页面元素"""
        self.logger.debug(f"定位元素: {selector}")
        return self.page.locator(selector)

    def get_by_test_id(self, test_id):
        """通过测试ID定位元素"""
        self.logger.debug(f"通过测试ID定位元素: {test_id}")
        return self.page.get_by_test_id(test_id)

    def get_by_role(self, role, name=None):
        """通过角色和名称定位元素"""
        self.logger.debug(f"通过角色和名称定位元素: role={role}, name={name}")
        return self.page.get_by_role(role, name=name)

    def get_by_placeholder(self, text):
        """通过占位符定位元素"""
        self.logger.debug(f"通过占位符定位元素: {text}")
        return self.page.get_by_placeholder(text)

    def get_by_label(self, text):
        """通过标签文本定位元素"""
        self.logger.debug(f"通过标签文本定位元素: {text}")
        return self.page.get_by_label(text)

    def get_by_text(self, text, exact=False):
        """通过文本内容定位元素"""
        self.logger.debug(f"通过文本内容定位元素: {text}")
        return self.page.get_by_text(text, exact=exact)

    def get_by_alt_text(self, text):
        """通过ALT文本定位元素"""
        self.logger.debug(f"通过ALT文本定位元素: {text}")
        return self.page.get_by_alt_text(text)

    def get_by_title(self, text):
        """通过标题定位元素"""
        self.logger.debug(f"通过标题定位元素: {text}")
        return self.page.get_by_title(text)

    def get_by_has_text(self, text, initial_selector='li', exact=False, final_selector=None):
        """通过文本过滤定位元素

        Args:
            initial_selector: # 初始定位的 CSS 选择器（如 "ul", "li"）
            text: 要过滤的文本值
            exact: 是否使用精确匹配（正则表达式），默认 False
            final_selector: 最终目标选择器，如 "span"，可选

        Returns:
            Locator: 元素定位器
        """
        try:
            desc = f"过滤选项: {initial_selector} -> {text}"
            if final_selector:
                desc += f" -> {final_selector}"

            self.logger.info(f"点击{desc}")

            # 根据是否精确匹配选择过滤方式
            if exact:
                import re
                element = self.locator(initial_selector).filter(has_text=re.compile(rf"^{text}$"))
            else:
                element = self.locator(initial_selector).filter(has_text=text)

            # 如果有最终选择器，继续定位
            if final_selector:
                element = element.locator(final_selector)

            self.logger.info(f"成功定位元素：{desc}")
            return element

        except Exception as e:
            self.logger.error(f"定位元素{desc}失败")
            raise

    def click(self, selector):
        """点击元素，自动处理文本选择器的引号"""
        try:
            original_selector = selector
            formatted_selector = _format_selector(selector)
            result = self.page.click(formatted_selector)
            self.logger.info(f"成功点击元素: {original_selector}")
            return result
        except Exception as e:
            self.logger.error(f"点击元素失败: {original_selector}")
            raise

    def click_text_element(self, text, exact=True):
        """点击包含指定文本的元素"""
        try:
            self.page.get_by_text(text, exact=exact).click()
            self.logger.info(f"成功点击文本元素: {text}")
        except Exception as e:
            self.logger.error(f"点击文本元素失败: {text}")
            raise

    def fill(self, selector, value):
        """输入文本"""
        # 类型安全转换
        if isinstance(value, (int, float)):
            value = str(value)
        try:
            result = self.page.fill(selector, value)
            self.logger.info(f"成功输入文本: selector={selector}, value={value}")
            return result
        except Exception as e:
            self.logger.error(f"输入文本失败: selector={selector}, value={value}")
            raise

    def hover(self, selector):
        """悬停元素"""
        try:
            original_selector = selector
            formatted_selector = _format_selector(selector)
            result = self.page.hover(formatted_selector)
            self.logger.debug(f"成功悬停元素: {original_selector}")
            return result
        except Exception as e:
            self.logger.error(f"悬停元素失败: {original_selector}")
            raise

class BasePage(Base):

    @property
    def popup(self):
        """定位页面弹窗元素"""
        return self.locator(".el-message__content")

    @property
    def btn_create(self):
        """定位页面新建按钮元素"""
        button_texts = ["新建", "创建集群", "立即创建"]  # 优先匹配更具体的文本

        for text in button_texts:
            buttons = self.get_by_text(text)
            print(buttons)
            print(1111)
            if buttons.count() > 0:
                return buttons

        raise Exception("未找到新建按钮")

    @property
    def input_search(self):
        """定位页面搜索框元素"""
        return self.locator(".input-with-select > .el-input__inner")

    @property
    def btn_search(self):
        """定位页面搜索按钮元素"""
        return self.get_by_text("搜索", exact=True)

    @property
    def btn_reset(self):
        """定位页面重置按钮元素"""
        return self.get_by_text("重置")

    @property
    def btn_refresh(self):
        """定位页面刷新按钮元素"""
        return self.locator("#serverRefresh")

    @property
    def dialog_confirm(self):
        """定位页面确定按钮元素"""
        return self.get_by_role("dialog").get_by_text("确定")

    @property
    def dialog_cancel(self):
        """定位页面取消按钮元素"""
        return self.get_by_role("dialog").get_by_text("确定")

    def close_dialog_if_exists(self):
        """如果存在对话框，则关闭它"""
        # 检查并关闭对话框
        dialog = self.get_by_role("button", name="Close")
        if dialog.is_visible():
            logger.info("发现未关闭的对话框，正在关闭...")
            dialog.click()
            # self.page.keyboard.press("Escape")    # 也可以按ESC键

    def search(self, keyword: str):
        """执行搜索操作"""
        try:
            self.logger.info(f"开始搜索: {keyword}")
            self.input_search.fill(keyword)
            self.btn_search.click()
            self.logger.info(f"搜索操作完成: {keyword}")
        except Exception as e:
            self.logger.error(f"搜索操作失败: keyword={keyword}")
            raise


    def navigate_to_service(self, service: str):
        """智能导航到指定服务，支持不同层级结构

        Args:
            service: 服务名称，如 '云容器引擎'、'云硬盘'、'物理服务器' 等

        Returns:
            bool: 导航是否成功
        """
        if service not in BaseElements.SERVICE_NAVIGATION_MAP:
            self.logger.error(f"未知的服务: {service}，请检查服务名称或更新导航映射表")
            return False

        try:
            navigation_path = BaseElements.SERVICE_NAVIGATION_MAP[service]

            if len(navigation_path) == 1:
                # 两层结构：基础设施 -> 服务
                root_menu = navigation_path[0]
                self.hover(root_menu)
                self.click(service)
                self.logger.info(f"成功导航到服务: {root_menu} -> {service}")

            elif len(navigation_path) == 2:
                # 三层结构：资源中心 -> 二级菜单 -> 服务
                root_menu, category = navigation_path
                self.hover(root_menu)
                self.hover(category)
                self.click(service)
                self.logger.info(f"成功导航到服务: {root_menu} -> {category} -> {service}")

            else:
                self.logger.error(f"服务 {service} 的导航路径配置错误: {navigation_path}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"导航到服务 {service} 失败: {e}")
            return False

    def assert_popup_success(self, text=None, timeout=5000):
        """根据弹窗文本和类型，断言操作成功

        Args:
            text: 期望的弹窗文本内容（可选）
            timeout: 超时时间
        """
        popup = self.popup
        expect(popup).to_be_visible(timeout=timeout)

        # 获取弹窗文本
        popup_text = popup.inner_text().strip()

        # 通过检查元素的CSS类名判断弹窗类型
        is_success = popup.evaluate("element => element.parentElement.classList.contains('el-message--success')")

        if not is_success:
            raise AssertionError(f"预期操作成功，但实际失败。弹窗文本: {popup_text}")

        if text:
            expect(popup).to_contain_text(text)

        expect(popup).not_to_be_visible(timeout=timeout)

    def assert_popup_error(self, text=None, timeout=5000):
        """根据弹窗文本和类型，断言操作失败

        Args:
            text: 期望的弹窗文本内容（可选）
            timeout: 超时时间
        """
        popup = self.popup
        expect(popup).to_be_visible(timeout=timeout)

        # 获取弹窗文本
        popup_text = popup.inner_text().strip()

        # 通过检查元素的CSS类名判断弹窗类型
        is_error = popup.evaluate("element => element.parentElement.classList.contains('el-message--error')")

        if not is_error:
            raise AssertionError(f"预期操作失败，但实际成功或其他状态。弹窗文本: {popup_text}")

        if text:
            expect(popup).to_contain_text(text)

        expect(popup).not_to_be_visible(timeout=timeout)

    def assert_list_contain(self, keyword, name_column_index=2):
        """
        断言页面表格列表中某一列至少有一个元素包含指定关键字
        """

        # 先获取所有行
        rows = self.locator("tbody tr")

        # 获取每行的资源名称
        actual_names = []
        if rows.count() > 0:
            for row in rows.all():
                name_cell = row.locator(f"td:nth-child({name_column_index})")
                name = name_cell.inner_text().strip()
                actual_names.append(name)

        matched = any(keyword in name for name in actual_names)
        assert matched, f"未找到包含关键字 '{keyword}' 的名称，实际名称列表: {actual_names}"

    def assert_resource_status(self, name: str, status='运行中', timeout=30000, name_column=2, target_column=0):
        """
        验证页面表格中指定资源的状态是否符合预期。

        Args:
            name: 资源名称
            status: 期望状态
            timeout: 超时时间
            name_column: 名称所在列（从1开始）
            target_column: 状态所在列（0表示整行匹配）
        """

        # 使用 getByRole 精确匹配行名称
        target_row = self.get_by_role("row", name=re.compile(rf"^{re.escape(name)}\s"))

        # 检查行是否存在，避免后续长时间等待
        try:
            expect(target_row).to_be_visible(timeout=5000)  # 短超时检查存在性
        except Exception:
            raise AssertionError(f"未找到名称为 '{name}' 的资源行")

        if target_column:
            column = target_row.locator(f"td:nth-child({target_column})")
            expect(column).to_contain_text(status, timeout=timeout)
        else:
            expect(target_row).to_contain_text(status, timeout=timeout)
